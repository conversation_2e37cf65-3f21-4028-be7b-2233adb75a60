import numpy as np
from scipy.ndimage import affine_transform
import rasterio
from rasterio.transform import Affine
import matplotlib.pyplot as plt

bip_path = 'Sample Datacubes/CommonMedicines/Common_Medicine.bip'
# bsq_path = 'Sample Datacubes/Pika_L_airborne_flight_datacubes/Pika L airborne flight D1.bsq'
bip_2_path = 'Sample Datacubes/CommonMedicines/Common_Medicine.bip'

with rasterio.open(bip_path) as src:
    datacube = src.read()
    band_r, band_g, band_b = 30, 20, 10

shear_x_deg = 20
shear_affine = Affine.shear(shear_x_deg, 0)
matrix = np.array([[shear_affine.a, shear_affine.b],
                   [shear_affine.d, shear_affine.e]])
inv_matrix = np.linalg.inv(matrix)

bands, height1, width1 = datacube.shape
sheared_datacube = np.empty_like(datacube)
for i in range(bands):
    sheared_datacube[i] = affine_transform(
        datacube[i], inv_matrix, offset=0, output_shape=(height1, width1), order=1
    )

with rasterio.open(bip_2_path) as src2:
    datacube2 = src2.read()
    bands2, height2, width2 = datacube2.shape

# --- Ensure both datacubes have the same number of bands ---
if bands2 != bands:
    min_bands = min(bands, bands2)
    sheared_datacube = sheared_datacube[:min_bands]
    datacube2 = datacube2[:min_bands]
    bands = min_bands

# --- Crop both datacubes to the minimum height ---
min_height = min(sheared_datacube.shape[1], datacube2.shape[1])
sheared_datacube = sheared_datacube[:, :min_height, :]
datacube2 = datacube2[:, :min_height, :]

# --- Concatenate along width (axis=2) ---
final_datacube = np.concatenate((sheared_datacube, datacube2), axis=2)

# --- Select bands for RGB ---
r = final_datacube[band_r - 1]
g = final_datacube[band_g - 1]
b = final_datacube[band_b - 1]
rgb = np.dstack((r, g, b))

# --- Normalize and display ---
rgb_min, rgb_max = rgb.min(), rgb.max()
rgb_norm = (rgb - rgb_min) / (rgb_max - rgb_min)

plt.figure(figsize=(12, 8))
plt.imshow(rgb_norm)
plt.title('Mosaicked Sheared RGB Composite')
plt.axis('off')
plt.show()
