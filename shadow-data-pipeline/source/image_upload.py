import os
from collections import defaultdict

from internal import constants
from internal import s3gateway
from internal import upload_report

def load_files(dir_path):
    """Load all files from a directory"""
    files_by_extension = defaultdict(list)
    for filename in os.listdir(dir_path):
        if os.path.isfile(os.path.join(dir_path, filename)):
            _, ext = os.path.splitext(filename)
            ext = ext.lower()
            files_by_extension[ext].append(filename)
    return files_by_extension

def validate_imageset(allfiles, imageset_name):
    """Check if all files for a set exist"""
    missing_files = []
    if not (imageset_name + constants.METADATA_FILE_EXTENSION) in allfiles[constants.METADATA_FILE_EXTENSION]:
        missing_files.append(imageset_name + constants.METADATA_FILE_EXTENSION)
    if not (imageset_name + constants.BINARY_FILE_EXTENSION) in allfiles[constants.BINARY_FILE_EXTENSION]:
        missing_files.append(imageset_name + constants.BINARY_FILE_EXTENSION)
    if not (imageset_name + constants.HEADER_FILE_EXTENSION) in allfiles[constants.HEADER_FILE_EXTENSION]:
        missing_files.append(imageset_name + constants.HEADER_FILE_EXTENSION)
    
    if missing_files:
        return False, missing_files
    else:
        return True, []

def parse_name(filename):
    """Parse filename and determine its category and subcategory"""
    # Naming convention is: <category>_<subcategory>_<ID>.<extension>
    parts = filename.split('_')
    return parts[0], parts[1] # Return the first and second index

if __name__ == "__main__":
    # Make sure you have a .env file with:
    # AWS_ACCESS_KEY_ID=your_access_key
    # AWS_SECRET_ACCESS_KEY=your_secret_key
    print("Starting uploader")
    
    # Init S3 Gateway
    gateway = s3gateway.S3Gateway(constants.ROLE_ARN, constants.BUCKET_NAME)

    # Init final report struct
    upload_report = upload_report.upload_report()

    # Read from 'upload' folder
    allfiles = load_files("./upload")

    # Loop thru each file
    for file in allfiles['.json']: # Start from .json file list
        
        # Get name of this set
        imageset_name = os.path.splitext(file)[0]

        # Ensure that the .bip and .hdr file exist
        status, missingfiles = validate_imageset(allfiles, imageset_name)

        if not status:
            # Error, note into report and go next set
            upload_report.missing_files.append(missingfiles)
            upload_report.num_files_detected += (3 - missingfiles.count) # Add the remaining files detected
            continue
        else:
            upload_report.num_files_detected += 3 # Add 3 files detected
    
        # Deduce category and subcategory from name
        category, subcategory = parse_name(imageset_name)     

        # Upload files with checks for duplicate file
        # Metadata file
        try: 
            gateway.upload_image(category, subcategory, imageset_name, constants.METADATA_FILE_EXTENSION)
            upload_report.num_files_uploaded += 1
        except s3gateway.UploadFailError as e:
            print(f"Error uploading to metadata file to {category}/{subcategory}/{imageset_name}: {e}")
            # Error, note into report and go next set

            # Because metadata failed, implicitly the binary and header also fails
            upload_report.num_files_failed_upload += 3
            failed_filename = f'{imageset_name}{constants.METADATA_FILE_EXTENSION}'
            upload_report.failed_upload_files.append({'file': failed_filename, 'error': e})

            failed_filename = f'{imageset_name}{constants.BINARY_FILE_EXTENSION}'
            upload_report.failed_upload_files.append({'file': failed_filename, 'error': e})

            failed_filename = f'{imageset_name}{constants.HEADER_FILE_EXTENSION}'
            upload_report.failed_upload_files.append({'file': failed_filename, 'error': e})
            continue
        except s3gateway.FileExistError as e:
            upload_report.num_files_exist += 1
            filename = f'{imageset_name}{constants.BINARY_FILE_EXTENSION}'
            upload_report.existing_files.append(filename)

        # Binary file
        try:
            gateway.upload_image(category, subcategory, imageset_name, constants.BINARY_FILE_EXTENSION)
            upload_report.num_files_uploaded += 1
        except s3gateway.UploadFailError as e:
            print(f"Error uploading to binary file to {category}/{subcategory}/{imageset_name}: {e}")
            # Error, note into report and go next set, at this point the metadata file has already been uploaded
            
            # Because binary failed, implicitly the header also fails
            upload_report.num_files_failed_upload += 2
            failed_filename = f'{imageset_name}{constants.BINARY_FILE_EXTENSION}'
            upload_report.failed_upload_files.append({'file': failed_filename, 'error': e})

            failed_filename = f'{imageset_name}{constants.HEADER_FILE_EXTENSION}'
            upload_report.failed_upload_files.append({'file': failed_filename, 'error': e})

            # Note metadata file as mismatched
            mismatched_filename = f'{imageset_name}{constants.METADATA_FILE_EXTENSION}'
            upload_report.num_mismatched_files += 1
            upload_report.mismatched_files.append(mismatched_filename)
            continue
        except s3gateway.FileExistError as e:
            upload_report.num_files_exist += 1
            filename = f'{imageset_name}{constants.BINARY_FILE_EXTENSION}'
            upload_report.existing_files.append(filename)

        # Header file
        try:
            gateway.upload_image(category, subcategory, imageset_name, constants.HEADER_FILE_EXTENSION)
            upload_report.num_files_uploaded += 1
        except s3gateway.UploadFailError as e:
            print(f"Error uploading to header file to {category}/{subcategory}/{imageset_name}: {e}")
            # Error, note into report and go next set, at this point the metadata and binary files have already been uploaded
            failed_filename = f'{imageset_name}{constants.HEADER_FILE_EXTENSION}'
            upload_report.num_files_failed_upload += 1
            upload_report.failed_upload_files.append({'file': failed_filename, 'error': e})

            # Note metadata and binary file as mismatched
            upload_report.num_mismatched_files += 2

            mismatched_filename = f'{imageset_name}{constants.METADATA_FILE_EXTENSION}'
            upload_report.mismatched_files.append(mismatched_filename) 
            mismatched_filename = f'{imageset_name}{constants.BINARY_FILE_EXTENSION}'
            upload_report.mismatched_files.append(mismatched_filename) 
            continue
        except s3gateway.FileExistError as e:
            upload_report.num_files_exist += 1
            filename = f'{imageset_name}{constants.BINARY_FILE_EXTENSION}'
            upload_report.existing_files.append(filename)

    # Generate progress/output report
    upload_report.dump()
