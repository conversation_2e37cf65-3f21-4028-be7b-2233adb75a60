class upload_report:
    def __init__(self):
        self.num_files_detected = 0
        self.num_files_uploaded = 0
        self.num_files_missing = 0
        self.num_files_failed_upload = 0
        self.num_files_exist = 0
        self.num_mismatched_files = 0
        self.missing_files = [] # String array
        self.failed_upload_files = [] # [file, error] array
        self.existing_files = []
        self.mismatched_files = []

    def dump(self):
        print("=== Upload Report ===")
        print(f"Total files detected: {self.num_files_detected}")
        print(f"Total files successfully uploaded: {self.num_files_uploaded}")
        print(f"Total files missing: {self.num_files_missing}")
        if self.missing_files:
            print("Missing files details:")
            for missing in self.missing_files:
                print(f"  - {missing}")
        print(f"Total files failed to upload: {self.num_files_failed_upload}")
        if self.failed_upload_files:
            print("Failed upload files details:")
            for fail in self.failed_upload_files:
                print(f"  - {fail['file']} [{fail['error']}]")
        print(f"Total files that already exist on S3: {self.num_files_exist}")
        if self.existing_files:
            print("Exisiting files details:")
            for file in self.existing_files:
                print(f"  - {file}")
        print(f"Total files that are mismatched: {self.num_mismatched_files}")
        if self.mismatched_files:
            print("Mismatched files details:")
            for file in self.mismatched_files:
                print(f"  - {file}")