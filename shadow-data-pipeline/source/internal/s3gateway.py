import os
import boto3
import requests
import uuid
import time

from . import constants

class FileExistError(Exception):
    def __init__(self, message=None):
        super().__init__(message)

class UploadFailError(Exception):
    def __init__(self, message=None):
        super().__init__(message)


class S3Gateway:
    def __init__(self, role_arn, bucket_name, region='us-east-1'):
        self.role_arn = role_arn
        self.bucket_name = bucket_name
        self.region = region
        
        # Verify environment variables are loaded
        if not os.getenv('AWS_ACCESS_KEY_ID') or not os.getenv('AWS_SECRET_ACCESS_KEY'):
            raise ValueError("AWS credentials not found in environment variables")
    
    def assume_role(self):
        # Create STS client using .env credentials
        sts_client = boto3.client(
            'sts',
            aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
            aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY'),
            region_name=self.region
        )
        
        # Assume the role
        response = sts_client.assume_role(
            RoleArn=self.role_arn,
            RoleSessionName=f"image_upload-{uuid.uuid4()}",
            DurationSeconds=3600  # 1 hour
        )
        
        return response['Credentials']

    def check_object_exist(self, key):
        """Check if an object exists in the bucket"""
        credentials = self.assume_role()
        if not credentials:
            return False

        s3_client = boto3.client(
            's3',
            aws_access_key_id=credentials['AccessKeyId'],
            aws_secret_access_key=credentials['SecretAccessKey'],
            aws_session_token=credentials['SessionToken'],
            region_name=self.region
        )

        try:
            response = s3_client.head_object(Bucket=self.bucket_name, Key=key)
            return True
        except s3_client.exceptions.ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == '404' or error_code == 'NoSuchKey':
                # print(f"Object with key '{key}' does not exist (404).")
                return False
            else:
                raise ValueError(f'Unknown error when checking object existence')

    def generate_presigned_url(self, credentials, object_key, expiration=3600):
        s3_client = boto3.client(
            's3',
            aws_access_key_id=credentials['AccessKeyId'],
            aws_secret_access_key=credentials['SecretAccessKey'],
            aws_session_token=credentials['SessionToken'],
            region_name=self.region
        )

        # Generate presigned URL
        presigned_url = s3_client.generate_presigned_url(
            'put_object',
            Params={
                'Bucket': self.bucket_name,
                'Key': object_key
            },
            ExpiresIn=expiration
        )

        return presigned_url
        
    def upload_file_to_presigned_url(self, presigned_url, file_path):        
        print(f"Uploading file: {file_path}")
        
        # Read and upload file
        with open(file_path, 'rb') as f:
            response = requests.put(
                presigned_url,
                data=f,
            )
        
        if response.status_code == 200:
            print("Successfully uploaded file to S3")
            return True
        else:
            print(f"Upload failed with status code: {response.status_code}")
            print(f"Response: {response.text}")
            return False
        
    def upload_image_presigned(self, src_path, dst_path):
        """Upload an image"""
        # Step 1: Assume role
        credentials = self.assume_role()
        if not credentials:
            raise ValueError('Failed to assume IAM role')
        
        # Generate object key (S3 path)
        object_key = dst_path
        
        # Step 2: Generate presigned URL
        presigned_url = self.generate_presigned_url(credentials, object_key)
        if not presigned_url:
            raise ValueError('Failed to generate presigned URL')
        
        # Step 3: Upload file
        success = self.upload_file_to_presigned_url(presigned_url, src_path)
        if success:
            print(f"Image uploaded to: s3://{self.bucket_name}/{object_key}")
        else:
            raise ValueError('Failed to upload file')
        
    def upload_image(self, category, subcategory, imageset_name, file_extension, retry_delay = 1, retry_count = 0, max_retries = 5):        
        # Main folder (/metadata or /data)
        if file_extension is constants.METADATA_FILE_EXTENSION:
            main_folder = "metadata"
        elif file_extension is constants.BINARY_FILE_EXTENSION or file_extension is constants.HEADER_FILE_EXTENSION:
            main_folder = "data"

        # Check if files for the set already exists on S3 bucket, if not upload
        dst_path = f"{main_folder}/{category}/{subcategory}/{imageset_name}{file_extension}"
        if not self.check_object_exist(dst_path):
            # Upload file to set folder
            try: 
                self.upload_image_presigned(
                    f"upload/{imageset_name}{file_extension}",
                    dst_path
                )
            except ValueError as e:
                print(f'Image upload failed with error: {e}')
                if retry_count < max_retries:
                    # If fail, retry with backoff
                    time.sleep(retry_delay)
                    print(f'Retrying with backoff {retry_delay}')
                    self.upload_image(category, subcategory, imageset_name, file_extension, retry_delay * 2, retry_count + 1)
                else:
                    print(f'Max retry attempts. Moving to next file.')
                    raise UploadFailError(f"Failed to upload {imageset_name}{file_extension} file after retries")
        else:
            raise FileExistError(f"File already exists on bucket")
            