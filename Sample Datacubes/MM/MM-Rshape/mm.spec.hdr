ENVI
reflectance scale factor = 4095
bands = 80
lines = 1
interleave = bip
samples = 1
data type = 12
wavelength= {397.24, 403.348, 409.456, 415.564, 421.672, 427.78, 433.888, 439.996, 446.104, 452.212, 458.32, 464.428, 470.536, 476.644, 482.752, 488.86, 494.968, 501.076, 507.184, 513.292, 519.4, 525.508, 531.616, 537.724, 543.832, 549.94, 556.048, 562.156, 568.264, 574.372, 580.48, 586.588, 592.696, 598.804, 604.912, 611.02, 617.128, 623.236, 629.344, 635.452, 641.56, 647.668, 653.776, 659.884, 665.992, 672.1, 678.208, 684.316, 690.424, 696.532, 702.64, 708.748, 714.856, 720.964, 727.072, 733.18, 739.288, 745.396, 751.504, 757.612, 763.72, 769.828, 775.936, 782.044, 788.152, 794.26, 800.368, 806.476, 812.584, 818.692, 824.8, 830.908, 837.016, 843.124, 849.232, 855.34, 861.448, 867.556, 873.664, 879.772}
original cube file = MM-Rshape.bip
pointlist= {(517, 195), (517, 196), (517, 197), (517, 198), (517, 199), (517, 200), (517, 201), (517, 202), (517, 203), (517, 204), (517, 205), (517, 206), (517, 207), (517, 208), (517, 209), (517, 210), (517, 211), (517, 212), (517, 213), (517, 214), (517, 215), (518, 195), (518, 196), (518, 197), (518, 198), (518, 199), (518, 200), (518, 201), (518, 202), (518, 203), (518, 204), (518, 205), (518, 206), (518, 207), (518, 208), (518, 209), (518, 210), (518, 211), (518, 212), (518, 213), (518, 214), (518, 215), (519, 195), (519, 196), (519, 197), (519, 198), (519, 199), (519, 200), (519, 201), (519, 202), (519, 203), (519, 204), (519, 205), (519, 206), (519, 207), (519, 208), (519, 209), (519, 210), (519, 211), (519, 212), (519, 213), (519, 214), (519, 215), (520, 195), (520, 196), (520, 197), (520, 198), (520, 199), (520, 200), (520, 201), (520, 202), (520, 203), (520, 204), (520, 205), (520, 206), (520, 207), (520, 208), (520, 209), (520, 210), (520, 211), (520, 212), (520, 213), (520, 214), (520, 215), (521, 195), (521, 196), (521, 197), (521, 198), (521, 199), (521, 200), (521, 201), (521, 202), (521, 203), (521, 204), (521, 205), (521, 206), (521, 207), (521, 208), (521, 209), (521, 210), (521, 211), (521, 212), (521, 213), (521, 214), (521, 215), (522, 195), (522, 196), (522, 197), (522, 198), (522, 199), (522, 200), (522, 201), (522, 202), (522, 203), (522, 204), (522, 205), (522, 206), (522, 207), (522, 208), (522, 209), (522, 210), (522, 211), (522, 212), (522, 213), (522, 214), (522, 215), (523, 195), (523, 196), (523, 197), (523, 198), (523, 199), (523, 200), (523, 201), (523, 202), (523, 203), (523, 204), (523, 205), (523, 206), (523, 207), (523, 208), (523, 209), (523, 210), (523, 211), (523, 212), (523, 213), (523, 214), (523, 215), (524, 195), (524, 196), (524, 197), (524, 198), (524, 199), (524, 200), (524, 201), (524, 202), (524, 203), (524, 204), (524, 205), (524, 206), (524, 207), (524, 208), (524, 209), (524, 210), (524, 211), (524, 212), (524, 213), (524, 214), (524, 215), (525, 195), (525, 196), (525, 197), (525, 198), (525, 199), (525, 200), (525, 201), (525, 202), (525, 203), (525, 204), (525, 205), (525, 206), (525, 207), (525, 208), (525, 209), (525, 210), (525, 211), (525, 212), (525, 213), (525, 214), (525, 215), (526, 195), (526, 196), (526, 197), (526, 198), (526, 199), (526, 200), (526, 201), (526, 202), (526, 203), (526, 204), (526, 205), (526, 206), (526, 207), (526, 208), (526, 209), (526, 210), (526, 211), (526, 212), (526, 213), (526, 214), (526, 215), (527, 195), (527, 196), (527, 197), (527, 198), (527, 199), (527, 200), (527, 201), (527, 202), (527, 203), (527, 204), (527, 205), (527, 206), (527, 207), (527, 208), (527, 209), (527, 210), (527, 211), (527, 212), (527, 213), (527, 214), (527, 215), (528, 195), (528, 196), (528, 197), (528, 198), (528, 199), (528, 200), (528, 201), (528, 202), (528, 203), (528, 204), (528, 205), (528, 206), (528, 207), (528, 208), (528, 209), (528, 210), (528, 211), (528, 212), (528, 213), (528, 214), (528, 215), (529, 195), (529, 196), (529, 197), (529, 198), (529, 199), (529, 200), (529, 201), (529, 202), (529, 203), (529, 204), (529, 205), (529, 206), (529, 207), (529, 208), (529, 209), (529, 210), (529, 211), (529, 212), (529, 213), (529, 214), (529, 215), (530, 195), (530, 196), (530, 197), (530, 198), (530, 199), (530, 200), (530, 201), (530, 202), (530, 203), (530, 204), (530, 205), (530, 206), (530, 207), (530, 208), (530, 209), (530, 210), (530, 211), (530, 212), (530, 213), (530, 214), (530, 215), (531, 195), (531, 196), (531, 197), (531, 198), (531, 199), (531, 200), (531, 201), (531, 202), (531, 203), (531, 204), (531, 205), (531, 206), (531, 207), (531, 208), (531, 209), (531, 210), (531, 211), (531, 212), (531, 213), (531, 214), (531, 215), (532, 195), (532, 196), (532, 197), (532, 198), (532, 199), (532, 200), (532, 201), (532, 202), (532, 203), (532, 204), (532, 205), (532, 206), (532, 207), (532, 208), (532, 209), (532, 210), (532, 211), (532, 212), (532, 213), (532, 214), (532, 215), (533, 195), (533, 196), (533, 197), (533, 198), (533, 199), (533, 200), (533, 201), (533, 202), (533, 203), (533, 204), (533, 205), (533, 206), (533, 207), (533, 208), (533, 209), (533, 210), (533, 211), (533, 212), (533, 213), (533, 214), (533, 215), (534, 195), (534, 196), (534, 197), (534, 198), (534, 199), (534, 200), (534, 201), (534, 202), (534, 203), (534, 204), (534, 205), (534, 206), (534, 207), (534, 208), (534, 209), (534, 210), (534, 211), (534, 212), (534, 213), (534, 214), (534, 215), (535, 195), (535, 196), (535, 197), (535, 198), (535, 199), (535, 200), (535, 201), (535, 202), (535, 203), (535, 204), (535, 205), (535, 206), (535, 207), (535, 208), (535, 209), (535, 210), (535, 211), (535, 212), (535, 213), (535, 214), (535, 215), (536, 195), (536, 196), (536, 197), (536, 198), (536, 199), (536, 200), (536, 201), (536, 202), (536, 203), (536, 204), (536, 205), (536, 206), (536, 207), (536, 208), (536, 209), (536, 210), (536, 211), (536, 212), (536, 213), (536, 214), (536, 215), (537, 195), (537, 196), (537, 197), (537, 198), (537, 199), (537, 200), (537, 201), (537, 202), (537, 203), (537, 204), (537, 205), (537, 206), (537, 207), (537, 208), (537, 209), (537, 210), (537, 211), (537, 212), (537, 213), (537, 214), (537, 215), (538, 195), (538, 196), (538, 197), (538, 198), (538, 199), (538, 200), (538, 201), (538, 202), (538, 203), (538, 204), (538, 205), (538, 206), (538, 207), (538, 208), (538, 209), (538, 210), (538, 211), (538, 212), (538, 213), (538, 214), (538, 215)}
boundary= {(517, 195), (517, 216), (539, 216), (539, 195)}
label color = #8DA0CB
