ENVI
reflectance scale factor = 4095
bands = 80
lines = 1
interleave = bip
samples = 1
data type = 12
wavelength= {397.24, 403.348, 409.456, 415.564, 421.672, 427.78, 433.888, 439.996, 446.104, 452.212, 458.32, 464.428, 470.536, 476.644, 482.752, 488.86, 494.968, 501.076, 507.184, 513.292, 519.4, 525.508, 531.616, 537.724, 543.832, 549.94, 556.048, 562.156, 568.264, 574.372, 580.48, 586.588, 592.696, 598.804, 604.912, 611.02, 617.128, 623.236, 629.344, 635.452, 641.56, 647.668, 653.776, 659.884, 665.992, 672.1, 678.208, 684.316, 690.424, 696.532, 702.64, 708.748, 714.856, 720.964, 727.072, 733.18, 739.288, 745.396, 751.504, 757.612, 763.72, 769.828, 775.936, 782.044, 788.152, 794.26, 800.368, 806.476, 812.584, 818.692, 824.8, 830.908, 837.016, 843.124, 849.232, 855.34, 861.448, 867.556, 873.664, 879.772}
original cube file = MM-Rshape.bip
pointlist= {(587, 125), (587, 126), (587, 127), (587, 128), (587, 129), (587, 130), (587, 131), (587, 132), (587, 133), (587, 134), (587, 135), (587, 136), (587, 137), (587, 138), (587, 139), (588, 125), (588, 126), (588, 127), (588, 128), (588, 129), (588, 130), (588, 131), (588, 132), (588, 133), (588, 134), (588, 135), (588, 136), (588, 137), (588, 138), (588, 139), (589, 125), (589, 126), (589, 127), (589, 128), (589, 129), (589, 130), (589, 131), (589, 132), (589, 133), (589, 134), (589, 135), (589, 136), (589, 137), (589, 138), (589, 139), (590, 125), (590, 126), (590, 127), (590, 128), (590, 129), (590, 130), (590, 131), (590, 132), (590, 133), (590, 134), (590, 135), (590, 136), (590, 137), (590, 138), (590, 139), (591, 125), (591, 126), (591, 127), (591, 128), (591, 129), (591, 130), (591, 131), (591, 132), (591, 133), (591, 134), (591, 135), (591, 136), (591, 137), (591, 138), (591, 139), (592, 125), (592, 126), (592, 127), (592, 128), (592, 129), (592, 130), (592, 131), (592, 132), (592, 133), (592, 134), (592, 135), (592, 136), (592, 137), (592, 138), (592, 139), (593, 125), (593, 126), (593, 127), (593, 128), (593, 129), (593, 130), (593, 131), (593, 132), (593, 133), (593, 134), (593, 135), (593, 136), (593, 137), (593, 138), (593, 139), (594, 125), (594, 126), (594, 127), (594, 128), (594, 129), (594, 130), (594, 131), (594, 132), (594, 133), (594, 134), (594, 135), (594, 136), (594, 137), (594, 138), (594, 139), (595, 125), (595, 126), (595, 127), (595, 128), (595, 129), (595, 130), (595, 131), (595, 132), (595, 133), (595, 134), (595, 135), (595, 136), (595, 137), (595, 138), (595, 139), (596, 125), (596, 126), (596, 127), (596, 128), (596, 129), (596, 130), (596, 131), (596, 132), (596, 133), (596, 134), (596, 135), (596, 136), (596, 137), (596, 138), (596, 139), (597, 125), (597, 126), (597, 127), (597, 128), (597, 129), (597, 130), (597, 131), (597, 132), (597, 133), (597, 134), (597, 135), (597, 136), (597, 137), (597, 138), (597, 139), (598, 125), (598, 126), (598, 127), (598, 128), (598, 129), (598, 130), (598, 131), (598, 132), (598, 133), (598, 134), (598, 135), (598, 136), (598, 137), (598, 138), (598, 139), (599, 125), (599, 126), (599, 127), (599, 128), (599, 129), (599, 130), (599, 131), (599, 132), (599, 133), (599, 134), (599, 135), (599, 136), (599, 137), (599, 138), (599, 139), (600, 125), (600, 126), (600, 127), (600, 128), (600, 129), (600, 130), (600, 131), (600, 132), (600, 133), (600, 134), (600, 135), (600, 136), (600, 137), (600, 138), (600, 139), (601, 125), (601, 126), (601, 127), (601, 128), (601, 129), (601, 130), (601, 131), (601, 132), (601, 133), (601, 134), (601, 135), (601, 136), (601, 137), (601, 138), (601, 139)}
boundary= {(587, 125), (587, 140), (602, 140), (602, 125)}
label color = #FFD92F
