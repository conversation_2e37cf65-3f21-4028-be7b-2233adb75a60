{'renderlist': {'rend1': {'filter': {'Params': {'hp': 98.0, 'inverse': False, 'lp': 2.0, 'solobands': True},
                                     'Plugin': 'resui.tools.filterfuncs.TwoPercent'},
                          'render': {'Params': {'blueband': 460, 'greenband': 550, 'redband': 640},
                                     'Plugin': 'resui.tools.renderfuncs.TrueColor'}}},
 'subcubes': {'cube2': {'cubePlugin': {'Params': {'member0': u'Spectrum("mm.spec")',
                                                  'member1': u'Spectrum("reeses.spec")',
                                                  'membercount': 2},
                                       'Plugin': 'resui.tools.multiple_sam.MultiSam'},
                        'renderlist': {'rend2': {'filter': {'Params': {},
                                                            'Plugin': 'resui.tools.filterfuncs.NoStretch'},
                                                 'render': {'Params': {'thresh0': 0.12157399787008762,
                                                                       'thresh1': 0.12244254168868064},
                                                            'Plugin': 'plugins.render.threshtocolor.ThreshToColor'}}}}}}