import boto3
import requests
import os
from datetime import datetime
import uuid
from pathlib import Path
from botocore.exceptions import ClientError
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class LaptopImageUploader:
    def __init__(self, role_arn, bucket_name, region='us-east-1'):
        self.role_arn = role_arn
        self.bucket_name = bucket_name
        self.region = region
        
        # Verify environment variables are loaded
        if not os.getenv('AWS_ACCESS_KEY_ID') or not os.getenv('AWS_SECRET_ACCESS_KEY'):
            raise ValueError("AWS credentials not found in environment variables")
    
    def assume_role(self, session_name=None):
        """
        Step 1: Assume the IAM role using credentials from .env
        
        Returns:
            dict: Temporary credentials or None if failed
        """
        try:
            if not session_name:
                session_name = f"LaptopUpload-{uuid.uuid4()}"
            
            # Create STS client using .env credentials
            sts_client = boto3.client(
                'sts',
                aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
                aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY'),
                region_name=self.region
            )
            
            print(f"Assuming role: {self.role_arn}")
            
            # Assume the role
            response = sts_client.assume_role(
                RoleArn=self.role_arn,
                RoleSessionName=session_name,
                DurationSeconds=3600  # 1 hour
            )
            
            print("✓ Successfully assumed role")
            return response['Credentials']
            
        except ClientError as e:
            print(f"✗ Error assuming role: {e}")
            return None
    
    def generate_presigned_url(self, credentials, object_key, expiration=3600):
        """
        Step 2: Generate presigned URL using temporary credentials from assumed role
        
        Args:
            credentials (dict): Temporary credentials from assume_role()
            object_key (str): S3 object key where file will be stored
            expiration (int): URL expiration in seconds
            
        Returns:
            str: Presigned URL or None if failed
        """
        try:
            # Create S3 client with temporary credentials
            s3_client = boto3.client(
                's3',
                aws_access_key_id=credentials['AccessKeyId'],
                aws_secret_access_key=credentials['SecretAccessKey'],
                aws_session_token=credentials['SessionToken'],
                region_name=self.region
            )
            
            print(f"Generating presigned URL for: {object_key}")
            
            # Generate presigned URL
            presigned_url = s3_client.generate_presigned_url(
                'put_object',
                Params={
                    'Bucket': self.bucket_name,
                    'Key': object_key
                },
                ExpiresIn=expiration
            )
            
            print("✓ Successfully generated presigned URL")
            return presigned_url
            
        except ClientError as e:
            print(f"✗ Error generating presigned URL: {e}")
            return None
    
    def upload_file_to_presigned_url(self, presigned_url, file_path):
        """
        Step 3: Upload file to S3 using the presigned URL
        
        Args:
            presigned_url (str): Presigned URL from generate_presigned_url()
            file_path (str): Local path to file to upload
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            file_extension = Path(file_path).suffix.lower()
            
            print(f"Uploading file: {file_path}")
            
            # Read and upload file
            with open(file_path, 'rb') as f:
                response = requests.put(
                    presigned_url,
                    data=f,
                )
            
            if response.status_code == 200:
                print("✓ Successfully uploaded file to S3")
                return True
            else:
                print(f"✗ Upload failed with status code: {response.status_code}")
                print(f"Response: {response.text}")
                return False
                
        except FileNotFoundError:
            print(f"✗ File not found: {file_path}")
            return False
        except Exception as e:
            print(f"✗ Error uploading file: {e}")
            return False
    
    def upload_image(self, image_path, dst_path):
        """
        Complete flow: Assume role → Generate presigned URL → Upload file
        
        Args:
            image_path (str): Local path to image file
            user_id (str): Optional user ID for organizing uploads
            custom_filename (str): Optional custom filename
            
        Returns:
            str: S3 object key if successful, None if failed
        """
        try:
            # Step 1: Assume role
            credentials = self.assume_role()
            if not credentials:
                return None
            
            # Generate object key (S3 path)
            object_key = dst_path
            
            # Step 2: Generate presigned URL
            presigned_url = self.generate_presigned_url(credentials, object_key)
            if not presigned_url:
                return None

            print(f'Presigned URL: [{presigned_url}]')
            
            # Step 3: Upload file
            success = self.upload_file_to_presigned_url(presigned_url, image_path)
            if success:
                print(f"✓ Image uploaded to: s3://{self.bucket_name}/{object_key}")
                return object_key
            else:
                return None
                
        except Exception as e:
            print(f"✗ Upload process failed: {e}")
            return None

def simple_upload(src_path, dst_path):
    """Simple example of the complete flow"""   
    
    # Configuration
    ROLE_ARN = os.getenv('ROLE_ARN')
    BUCKET_NAME = "donovans3bucket"
    
    # Create uploader
    uploader = LaptopImageUploader(ROLE_ARN, BUCKET_NAME)
    
    # Upload image (complete flow)
    object_key = uploader.upload_image(src_path, dst_path)
    
    if object_key:
        print(f"Success! Image available at: s3://{BUCKET_NAME}/{object_key}")
    else:
        print("Upload failed!")

if __name__ == "__main__":
    # Make sure you have a .env file with:
    # AWS_ACCESS_KEY_ID=your_access_key
    # AWS_SECRET_ACCESS_KEY=your_secret_key

    # Inputs to script
    category = "materials"
    subcategory = "metallic"
    image_id = "0001"

    # Determine each file name and upload
    image_name = category + "_" + subcategory + "_" + image_id

    # Binary file
    bin_filename = image_name + ".bip"
    src_path = bin_filename
    dst_path = "data/" + category + "/" + subcategory + "/" + bin_filename
    simple_upload(src_path, dst_path)

    # Header file
    bin_filename = image_name + ".hdr"
    src_path = bin_filename
    dst_path = "data/" + category + "/" + subcategory + "/" + bin_filename
    simple_upload(src_path, dst_path)

    # Metadata file
    bin_filename = image_name + ".json"
    src_path = bin_filename
    dst_path = "metadata/" + category + "/" + subcategory + "/" + bin_filename
    simple_upload(src_path, dst_path)


