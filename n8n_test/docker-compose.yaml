version: '3.8'

volumes:
  db_storage:
  n8n_shadow_storage:

services:
  postgres:
    image: postgres:16
    restart: always
    environment:
      - POSTGRES_USER
      - POSTGRES_PASSWORD
      - POSTGRES_DB
      - POSTGRES_NON_ROOT_USER
      - POSTGRES_NON_ROOT_PASSWORD
    volumes:
      - db_storage:/var/lib/postgresql/data
      - ./init-data.sh:/docker-entrypoint-initdb.d/init-data.sh
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -h localhost -U ${POSTGRES_USER} -d ${POSTGRES_DB}']
      interval: 5s
      timeout: 5s
      retries: 10

  n8n:
    #image: docker.n8n.io/n8nio/n8n
    build: 
      context: .
      dockerfile: Dockerfile
    restart: always
    environment:
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_DATABASE=${POSTGRES_DB}
      - DB_POSTGRESDB_USER=${POSTGRES_NON_ROOT_USER}
      - DB_POSTGRESDB_PASSWORD=${POSTGRES_NON_ROOT_PASSWORD}
      - N8N_SECURE_COOKIE=false
      - NODE_FUNCTION_ALLOW_EXTERNAL=${NODE_FUNCTION_ALLOW_EXTERNAL}  
      - NODE_FUNCTION_ALLOW_BUILTIN=fs 
      - N8N_HOST=${HOST_IP}
      - WEBHOOK_URL=http://${HOST_IP}:5678/
    ports:
      - "0.0.0.0:5678:5678"
    links:
      - postgres
    volumes:
      - n8n_shadow_storage:/home/<USER>/.n8n
      - ./internal:/app/internal 
    depends_on:
      postgres:
        condition: service_healthy
  
  filebrowser:
    image: filebrowser/filebrowser:v2.32.0
    container_name: filebrowser
    volumes:
      - ./internal:/srv 
      - ./filebrowser/filebrowser.db:/database/filebrowser.db
      - ./filebrowser/settings.json:/config/settings.json
    environment:
      - PUID=$(id -u)
      - PGID=$(id -g)
    ports:
      - 8095:80
