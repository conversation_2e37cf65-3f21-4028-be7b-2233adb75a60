### Version 1

# import numpy as np
# import rasterio
# import matplotlib.pyplot as plt

# bip_path = 'Sample Datacubes/CommonMedicines/Common_Medicine.bip'

# with rasterio.open(bip_path) as src:
#     datacube = src.read()
#     # Example: select bands for RGB visualization
#     band_r, band_g, band_b = 30, 20, 10

# bands, height, width = datacube.shape
# midpoint = width // 2

# left_half = datacube[:, :, :midpoint]
# right_half = datacube[:, :, midpoint:]

# left_flipped = np.flip(left_half, axis=2)
# right_flipped = np.flip(right_half, axis=2)

# flipped_datacube = np.concatenate((left_flipped, right_flipped), axis=2)

# # Select bands for RGB (adjust as needed)
# r = flipped_datacube[band_r - 1]
# g = flipped_datacube[band_g - 1]
# b = flipped_datacube[band_b - 1]
# rgb = np.dstack((r, g, b))

# # Normalize for display
# rgb_min, rgb_max = rgb.min(), rgb.max()
# rgb_norm = (rgb - rgb_min) / (rgb_max - rgb_min)

# plt.figure(figsize=(12, 8))
# plt.imshow(rgb_norm)
# plt.title('Vertically Split and Flipped RGB Composite')
# plt.axis('off')
# plt.show()

### Version 2 

import numpy as np
import rasterio
import matplotlib.pyplot as plt

# Load the hyperspectral datacube
bip_path = 'Sample Datacubes/CommonMedicines/Common_Medicine.bip'
with rasterio.open(bip_path) as src:
    datacube = src.read()
    band_r, band_g, band_b = 30, 20, 10  # Adjust as needed

# Split and flip
bands, height, width = datacube.shape
midpoint = width // 2
left_half = datacube[:, :, :midpoint]
right_half = datacube[:, :, midpoint:]
left_flipped = np.flip(left_half, axis=2)
right_flipped = np.flip(right_half, axis=2)
flipped_datacube = np.concatenate((left_flipped, right_flipped), axis=2)

# Prepare RGB for original and flipped images
def get_rgb(datacube, band_r, band_g, band_b):
    r = datacube[band_r - 1]
    g = datacube[band_g - 1]
    b = datacube[band_b - 1]
    rgb = np.dstack((r, g, b))
    rgb_min, rgb_max = rgb.min(), rgb.max()
    return (rgb - rgb_min) / (rgb_max - rgb_min)

rgb_norm_orig = get_rgb(datacube, band_r, band_g, band_b)
rgb_norm_flip = get_rgb(flipped_datacube, band_r, band_g, band_b)

# Display side by side
fig, axes = plt.subplots(1, 2, figsize=(20, 10))
axes[0].imshow(rgb_norm_orig)
axes[0].set_title('Original Image')
axes[0].axis('off')

axes[1].imshow(rgb_norm_flip)
axes[1].set_title('Vertically Split and Flipped Image')
axes[1].axis('off')

plt.show()
